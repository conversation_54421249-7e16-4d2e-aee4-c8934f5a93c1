// models/Approval.js
const approvalSchema = new mongoose.Schema({
  expenseId: { type: mongoose.Schema.Types.ObjectId, ref: 'Expense', required: true },
  adminId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  status: { type: String, enum: ['approved', 'rejected'], required: true },
  remarks: { type: String },
  updatedAt: { type: Date, default: Date.now }
});

module.exports = mongoose.model('Approval', approvalSchema);