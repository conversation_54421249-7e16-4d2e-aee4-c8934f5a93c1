import { useState } from 'react';
import '../css/pages/Dashboard.css';

function EmployeeDashboard() {
    const [activeTab, setActiveTab] = useState('submit');
    const [expenses, setExpenses] = useState([
        {
            id: 1,
            amount: 850,
            category: 'Travel',
            description: 'Taxi fare to client meeting',
            date: '2024-01-15',
            status: 'Approved',
            receipt: null
        },
        {
            id: 2,
            amount: 1200,
            category: 'Meals',
            description: 'Team lunch with clients',
            date: '2024-01-14',
            status: 'Pending',
            receipt: 'receipt_001.pdf'
        },
        {
            id: 3,
            amount: 2500,
            category: 'Equipment',
            description: 'Laptop accessories',
            date: '2024-01-12',
            status: 'Reimbursed',
            receipt: 'receipt_002.pdf'
        }
    ]);

    const [newExpense, setNewExpense] = useState({
        amount: '',
        category: '',
        description: '',
        receipt: null
    });

    const handleSubmitExpense = (e) => {
        e.preventDefault();
        const expense = {
            id: expenses.length + 1,
            ...newExpense,
            date: new Date().toISOString().split('T')[0],
            status: 'Pending'
        };
        setExpenses([expense, ...expenses]);
        setNewExpense({ amount: '', category: '', description: '', receipt: null });
        alert('Expense submitted successfully!');
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'Approved': return 'var(--accent-tertiary)';
            case 'Rejected': return 'var(--accent-secondary)';
            case 'Reimbursed': return 'var(--accent-primary)';
            default: return 'var(--text-secondary)';
        }
    };

    const tabs = [
        { id: 'submit', label: 'Submit Expense', icon: '➕' },
        { id: 'expenses', label: 'My Expenses', icon: '📋' },
        { id: 'reimbursements', label: 'Reimbursements', icon: '💰' }
    ];

    return (
        <div className="dashboard-container">
            <div className="dashboard-background">
                <div className="dashboard-particles"></div>
            </div>

            <div className="dashboard-content">
                <div className="dashboard-header">
                    <h1 className="dashboard-title">
                        Employee <span className="gradient-text">Dashboard</span>
                    </h1>
                    <p className="dashboard-subtitle">Manage your expenses and track reimbursements</p>
                </div>

                <div className="dashboard-tabs">
                    {tabs.map(tab => (
                        <button
                            key={tab.id}
                            className={`tab-btn ${activeTab === tab.id ? 'active' : ''}`}
                            onClick={() => setActiveTab(tab.id)}
                        >
                            <span className="tab-icon">{tab.icon}</span>
                            {tab.label}
                        </button>
                    ))}
                </div>

                <div className="dashboard-main">
                    {activeTab === 'submit' && (
                        <div className="tab-content">
                            <div className="content-card">
                                <h2 className="card-title">Submit New Expense</h2>
                                <form onSubmit={handleSubmitExpense} className="expense-form">
                                    <div className="form-row">
                                        <div className="form-group">
                                            <label className="form-label">Amount (₹)</label>
                                            <input
                                                type="number"
                                                className="form-input"
                                                value={newExpense.amount}
                                                onChange={(e) => setNewExpense({...newExpense, amount: e.target.value})}
                                                placeholder="0.00"
                                                required
                                            />
                                        </div>
                                        <div className="form-group">
                                            <label className="form-label">Category</label>
                                            <select
                                                className="form-input"
                                                value={newExpense.category}
                                                onChange={(e) => setNewExpense({...newExpense, category: e.target.value})}
                                                required
                                            >
                                                <option value="">Select Category</option>
                                                <option value="Travel">Travel</option>
                                                <option value="Meals">Meals</option>
                                                <option value="Equipment">Equipment</option>
                                                <option value="Office Supplies">Office Supplies</option>
                                                <option value="Training">Training</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div className="form-group">
                                        <label className="form-label">Description</label>
                                        <textarea
                                            className="form-input"
                                            value={newExpense.description}
                                            onChange={(e) => setNewExpense({...newExpense, description: e.target.value})}
                                            placeholder="Describe your expense..."
                                            rows="3"
                                            required
                                        />
                                    </div>
                                    {newExpense.amount > 1000 && (
                                        <div className="form-group">
                                            <label className="form-label">Receipt (Required for amounts > ₹1000)</label>
                                            <input
                                                type="file"
                                                className="form-input file-input"
                                                accept=".pdf,.jpg,.jpeg,.png"
                                                onChange={(e) => setNewExpense({...newExpense, receipt: e.target.files[0]})}
                                                required
                                            />
                                            <small className="form-help">Upload PDF or image file</small>
                                        </div>
                                    )}
                                    <button type="submit" className="submit-btn">
                                        <span className="btn-icon">📤</span>
                                        Submit Expense
                                    </button>
                                </form>
                            </div>
                        </div>
                    )}

                    {activeTab === 'expenses' && (
                        <div className="tab-content">
                            <div className="content-card">
                                <h2 className="card-title">My Expenses</h2>
                                <div className="expenses-list">
                                    {expenses.map(expense => (
                                        <div key={expense.id} className="expense-item">
                                            <div className="expense-info">
                                                <div className="expense-header">
                                                    <span className="expense-amount">₹{expense.amount}</span>
                                                    <span 
                                                        className="expense-status"
                                                        style={{ color: getStatusColor(expense.status) }}
                                                    >
                                                        {expense.status}
                                                    </span>
                                                </div>
                                                <div className="expense-details">
                                                    <span className="expense-category">{expense.category}</span>
                                                    <span className="expense-date">{expense.date}</span>
                                                </div>
                                                <p className="expense-description">{expense.description}</p>
                                            </div>
                                            <div className="expense-actions">
                                                {expense.status === 'Pending' && (
                                                    <>
                                                        <button className="action-btn edit-btn">✏️ Edit</button>
                                                        <button className="action-btn delete-btn">🗑️ Delete</button>
                                                    </>
                                                )}
                                                {expense.receipt && (
                                                    <button className="action-btn view-btn">📄 View Receipt</button>
                                                )}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    )}

                    {activeTab === 'reimbursements' && (
                        <div className="tab-content">
                            <div className="content-card">
                                <h2 className="card-title">Reimbursement Status</h2>
                                <div className="reimbursement-summary">
                                    <div className="summary-card">
                                        <div className="summary-icon">💰</div>
                                        <div className="summary-info">
                                            <span className="summary-label">Total Reimbursed</span>
                                            <span className="summary-value">₹2,500</span>
                                        </div>
                                    </div>
                                    <div className="summary-card">
                                        <div className="summary-icon">⏳</div>
                                        <div className="summary-info">
                                            <span className="summary-label">Pending Amount</span>
                                            <span className="summary-value">₹2,050</span>
                                        </div>
                                    </div>
                                    <div className="summary-card">
                                        <div className="summary-icon">✅</div>
                                        <div className="summary-info">
                                            <span className="summary-label">Approved Amount</span>
                                            <span className="summary-value">₹850</span>
                                        </div>
                                    </div>
                                </div>
                                <div className="reimbursement-list">
                                    {expenses.filter(e => e.status === 'Reimbursed').map(expense => (
                                        <div key={expense.id} className="reimbursement-item">
                                            <div className="reimbursement-info">
                                                <span className="reimbursement-amount">₹{expense.amount}</span>
                                                <span className="reimbursement-category">{expense.category}</span>
                                                <span className="reimbursement-date">{expense.date}</span>
                                            </div>
                                            <button className="action-btn download-btn">📥 Download Proof</button>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}

export default EmployeeDashboard;
