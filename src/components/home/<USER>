import '../../css/components/home/<USER>';

function UseCases() {
    const useCases = [
        {
            icon: "💼",
            title: "Professionals",
            description: "Track business expenses, manage receipts, and optimize tax deductions effortlessly.",
            benefits: ["Expense reports", "Tax optimization", "Receipt management"]
        },
        {
            icon: "👨‍🎓",
            title: "Students",
            description: "Monitor spending, stick to budgets, and develop healthy financial habits early.",
            benefits: ["Budget tracking", "Spending alerts", "Financial education"]
        },
        {
            icon: "🏠",
            title: "Families",
            description: "Coordinate household expenses, plan for goals, and teach kids about money.",
            benefits: ["Shared budgets", "Goal planning", "Family insights"]
        },
        {
            icon: "🚀",
            title: "Entrepreneurs",
            description: "Separate personal and business expenses, track investments, and plan growth.",
            benefits: ["Business tracking", "Investment monitoring", "Growth planning"]
        }
    ];

    const testimonials = [
        {
            quote: "This app helped me save 20% more every month! The insights are incredible.",
            author: "<PERSON>",
            role: "Marketing Manager",
            avatar: "👩‍💼",
            rating: 5
        },
        {
            quote: "Finally, an expense tracker that doesn't feel like work. Love the AI features!",
            author: "<PERSON>",
            role: "Software Developer",
            avatar: "👨‍💻",
            rating: 5
        },
        {
            quote: "Perfect for our family budget. The kids love tracking their allowances too!",
            author: "Emily Rodriguez",
            role: "Teacher & Mom",
            avatar: "👩‍🏫",
            rating: 5
        }
    ];

    return (
        <section className="use-cases-section">
            <div className="container">
                <div className="section-header">
                    <h2 className="section-title fade-in-up">
                        Perfect For <span className="gradient-text">Everyone</span>
                    </h2>
                    <p className="section-subtitle fade-in-up">
                        Whether you're just starting out or managing complex finances, we've got you covered
                    </p>
                </div>

                <div className="use-cases-grid">
                    {useCases.map((useCase, index) => (
                        <div key={index} className="use-case-card glass-card fade-in-up">
                            <div className="use-case-icon">{useCase.icon}</div>
                            <h3 className="use-case-title">{useCase.title}</h3>
                            <p className="use-case-description">{useCase.description}</p>
                            <ul className="use-case-benefits">
                                {useCase.benefits.map((benefit, idx) => (
                                    <li key={idx}>✓ {benefit}</li>
                                ))}
                            </ul>
                        </div>
                    ))}
                </div>

                <div className="testimonials-section">
                    <h3 className="testimonials-title fade-in-up">What Our Users Say</h3>
                    <div className="testimonials-grid">
                        {testimonials.map((testimonial, index) => (
                            <div key={index} className="testimonial-card glass-card fade-in-up">
                                <div className="testimonial-rating">
                                    {[...Array(testimonial.rating)].map((_, i) => (
                                        <span key={i} className="star">⭐</span>
                                    ))}
                                </div>
                                <blockquote className="testimonial-quote">
                                    "{testimonial.quote}"
                                </blockquote>
                                <div className="testimonial-author">
                                    <span className="author-avatar">{testimonial.avatar}</span>
                                    <div className="author-info">
                                        <span className="author-name">{testimonial.author}</span>
                                        <span className="author-role">{testimonial.role}</span>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </section>
    );
}

export default UseCases;