import '../../css/components/home/<USER>';

function HowItWorks() {
    const steps = [
        {
            number: "01",
            title: "Create Your Account",
            description: "Sign up in seconds with your email or social login. No credit card required for the free trial.",
            icon: "👤",
            color: "var(--accent-primary)"
        },
        {
            number: "02",
            title: "Add Your Expenses",
            description: "Log expenses instantly with our smart categorization, receipt scanning, and voice input features.",
            icon: "💳",
            color: "var(--accent-tertiary)"
        },
        {
            number: "03",
            title: "Get Smart Insights",
            description: "View beautiful analytics, set budgets, and receive AI-powered recommendations for better financial health.",
            icon: "📈",
            color: "var(--accent-secondary)"
        }
    ];

    return (
        <section className="how-it-works-section">
            <div className="container">
                <div className="section-header">
                    <h2 className="section-title fade-in-up">
                        How It <span className="gradient-text">Works</span>
                    </h2>
                    <p className="section-subtitle fade-in-up">
                        Get started with expense tracking in just 3 simple steps
                    </p>
                </div>

                <div className="steps-container">
                    {steps.map((step, index) => (
                        <div key={index} className="step-card fade-in-up">
                            <div className="step-number" style={{ color: step.color }}>
                                {step.number}
                            </div>
                            <div className="step-icon">
                                {step.icon}
                            </div>
                            <div className="step-content">
                                <h3 className="step-title">{step.title}</h3>
                                <p className="step-description">{step.description}</p>
                            </div>
                            <div className="step-connector"></div>
                        </div>
                    ))}
                </div>

                <div className="demo-section fade-in-up">
                    <div className="demo-card glass-card">
                        <h3 className="demo-title">Ready to see it in action?</h3>
                        <p className="demo-description">
                            Watch our 2-minute demo to see how easy expense tracking can be
                        </p>
                        <button className="demo-btn gradient-btn">
                            <span className="btn-icon">▶</span>
                            Watch Demo
                        </button>
                    </div>
                </div>
            </div>
        </section>
    );
}

export default HowItWorks;
