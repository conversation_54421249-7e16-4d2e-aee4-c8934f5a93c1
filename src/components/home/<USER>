import '../../css/components/home/<USER>';

function Features() {
    const features = [
        {
            icon: "💰",
            title: "Smart Expense Tracking",
            description: "Effortlessly log expenses with AI-powered categorization and receipt scanning.",
            color: "var(--accent-tertiary)"
        },
        {
            icon: "📊",
            title: "Advanced Analytics",
            description: "Beautiful charts and insights to understand your spending patterns and trends.",
            color: "var(--accent-primary)"
        },
        {
            icon: "🔒",
            title: "Bank-Level Security",
            description: "Your financial data is protected with end-to-end encryption and privacy.",
            color: "var(--accent-secondary)"
        },
        {
            icon: "🎯",
            title: "Goal Setting",
            description: "Set budgets and savings goals with intelligent recommendations and alerts.",
            color: "var(--accent-quaternary)"
        },
        {
            icon: "📱",
            title: "Multi-Platform Sync",
            description: "Access your data seamlessly across all devices with real-time synchronization.",
            color: "var(--accent-tertiary)"
        },
        {
            icon: "🤖",
            title: "AI Insights",
            description: "Get personalized financial advice and spending optimization suggestions.",
            color: "var(--accent-primary)"
        }
    ];

    return (
        <section id="features" className="features-section">
            <div className="container">
                <div className="features-header">
                    <h2 className="section-title fade-in-up">
                        Why Choose <span className="gradient-text">ExpenseTracker</span>?
                    </h2>
                    <p className="section-subtitle fade-in-up">
                        Powerful features designed to make expense tracking effortless and insightful
                    </p>
                </div>

                <div className="features-grid">
                    {features.map((feature, index) => (
                        <div key={index} className="feature-card glass-card fade-in-up">
                            <div className="feature-icon" style={{ color: feature.color }}>
                                {feature.icon}
                            </div>
                            <h3 className="feature-title">{feature.title}</h3>
                            <p className="feature-description">{feature.description}</p>
                            <div className="feature-hover-effect"></div>
                        </div>
                    ))}
                </div>
            </div>
        </section>
    );
}

export default Features;