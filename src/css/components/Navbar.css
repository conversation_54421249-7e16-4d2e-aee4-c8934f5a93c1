.modern-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(10, 10, 10, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: var(--transition);
  padding: 0;
}

.navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  height: 70px;
}

.navbar-brand {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  color: var(--text-primary);
  font-weight: 700;
  font-size: 1.5rem;
  transition: var(--transition);
}

.navbar-brand:hover {
  color: var(--accent-primary);
  text-decoration: none;
  transform: scale(1.05);
}

.brand-icon {
  font-size: 2rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.brand-text {
  background: var(--gradient-tertiary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.navbar-menu {
  display: flex;
  align-items: center;
  gap: 2rem;
  flex: 1;
  justify-content: flex-end;
}

/* Ensure desktop menu always shows horizontally */
@media (min-width: 769px) {
  .navbar-menu {
    display: flex !important;
    position: static !important;
    flex-direction: row !important;
    background: transparent !important;
    backdrop-filter: none !important;
    border: none !important;
    padding: 0 !important;
    box-shadow: none !important;
  }
  
  .navbar-nav {
    display: flex !important;
    flex-direction: row !important;
    gap: 1.5rem !important;
    width: auto !important;
    margin-bottom: 0 !important;
  }
  
  .nav-link {
    width: auto !important;
    text-align: left !important;
    padding: 0.5rem 1rem !important;
    background: transparent !important;
    border: none !important;
  }
  
  .navbar-auth {
    display: flex !important;
    flex-direction: row !important;
    gap: 1rem !important;
    width: auto !important;
    border: none !important;
    padding: 0 !important;
  }
  
  .auth-btn {
    width: auto !important;
    padding: 10px 20px !important;
  }
  
  .mobile-menu-toggle {
    display: none !important;
  }
}

.navbar-nav {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  font-size: 1rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: var(--transition);
  position: relative;
}

.nav-link::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--gradient-tertiary);
  transition: var(--transition);
  transform: translateX(-50%);
}

.nav-link:hover {
  color: var(--text-primary);
  text-decoration: none;
  background: rgba(255, 255, 255, 0.05);
}

.nav-link:hover::before {
  width: 80%;
}

.navbar-auth {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.auth-btn {
  padding: 10px 20px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  transition: var(--transition);
  border: 1px solid transparent;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
}

.login-btn {
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.login-btn:hover {
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  text-decoration: none;
  transform: translateY(-1px);
}

.signup-btn {
  color: white;
  background: var(--gradient-tertiary);
  box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
}

.signup-btn:hover {
  color: white;
  text-decoration: none;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
  padding: 5px;
  z-index: 1001;
}

.mobile-menu-toggle span {
  width: 25px;
  height: 3px;
  background: var(--text-primary);
  border-radius: 2px;
  transition: var(--transition);
  transform-origin: center;
}

.mobile-menu-toggle span.active:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle span.active:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle span.active:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Navbar scroll effect */
.modern-navbar.scrolled {
  background: rgba(10, 10, 10, 0.98);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
}

/* Responsive design */
@media (max-width: 768px) {
  .navbar-container {
    padding: 1rem;
    position: relative;
  }
  
  .navbar-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(10, 10, 10, 0.98);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    flex-direction: column;
    padding: 1.5rem;
    gap: 1rem;
    z-index: 1000;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-height: calc(100vh - 70px);
    overflow-y: auto;
  }
  
  .navbar-menu.active {
    display: flex;
  }
  
  .navbar-nav {
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
    margin-bottom: 1rem;
  }
  
  .nav-link {
    width: 100%;
    text-align: center;
    padding: 0.8rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition);
  }
  
  .nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
  }
  
  .navbar-auth {
    flex-direction: column;
    gap: 0.8rem;
    width: 100%;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 1rem;
  }
  
  .auth-btn {
    width: 100%;
    padding: 12px 20px;
    text-align: center;
  }
  
  .mobile-menu-toggle {
    display: flex;
  }
  
  .brand-text {
    font-size: 1.2rem;
  }
  
  /* Ensure navbar doesn't interfere with content */
  .modern-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
  }
}

@media (max-width: 480px) {
  .navbar-container {
    padding: 0.8rem;
  }
  
  .navbar-brand {
    font-size: 1.3rem;
  }
  
  .brand-icon {
    font-size: 1.5rem;
  }
}
