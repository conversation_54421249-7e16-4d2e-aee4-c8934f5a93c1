.how-it-works-section {
  padding: 120px 0;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  position: relative;
  overflow: hidden;
}

.how-it-works-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 30% 70%, rgba(255, 107, 107, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 70% 30%, rgba(69, 183, 209, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.section-header {
  text-align: center;
  margin-bottom: 80px;
}

.steps-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
  margin-bottom: 80px;
  position: relative;
}

.steps-container::before {
  content: '';
  position: absolute;
  top: 120px;
  left: 15%;
  right: 15%;
  height: 2px;
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-tertiary), var(--accent-secondary));
  z-index: 0;
}

.step-card {
  flex: 1;
  text-align: center;
  position: relative;
  z-index: 1;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--border-radius);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: var(--transition);
}

.step-card:hover {
  transform: translateY(-10px);
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: var(--shadow-heavy);
}

.step-number {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1rem;
  opacity: 0.8;
  transition: var(--transition);
}

.step-card:hover .step-number {
  opacity: 1;
  transform: scale(1.1);
}

.step-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  display: block;
  transition: var(--transition);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.step-card:hover .step-icon {
  transform: scale(1.1) rotateY(10deg);
  filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.4));
}

.step-content {
  margin-top: 1rem;
}

.step-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
  transition: var(--transition);
}

.step-card:hover .step-title {
  color: var(--accent-primary);
}

.step-description {
  color: var(--text-secondary);
  line-height: 1.6;
  font-size: 1rem;
}

.demo-section {
  text-align: center;
}

.demo-card {
  max-width: 600px;
  margin: 0 auto;
  padding: 3rem;
  text-align: center;
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.demo-card:hover {
  background: rgba(255, 255, 255, 0.06);
  border-color: rgba(255, 255, 255, 0.15);
  transform: translateY(-5px);
  box-shadow: var(--shadow-heavy);
}

.demo-title {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.demo-description {
  color: var(--text-secondary);
  margin-bottom: 2rem;
  font-size: 1.1rem;
  line-height: 1.6;
}

.demo-btn {
  background: var(--gradient-secondary);
  border: none;
  border-radius: 50px;
  padding: 15px 35px;
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  transition: var(--transition);
  cursor: pointer;
  box-shadow: 0 8px 30px rgba(240, 147, 251, 0.3);
}

.demo-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(240, 147, 251, 0.4);
}

.demo-btn .btn-icon {
  font-size: 1.2rem;
  transition: var(--transition);
}

.demo-btn:hover .btn-icon {
  transform: scale(1.2);
}

/* Animation delays */
.step-card:nth-child(1) { animation-delay: 0.1s; }
.step-card:nth-child(2) { animation-delay: 0.2s; }
.step-card:nth-child(3) { animation-delay: 0.3s; }

/* Responsive design */
@media (max-width: 768px) {
  .how-it-works-section {
    padding: 80px 0;
  }
  
  .steps-container {
    flex-direction: column;
    gap: 3rem;
  }
  
  .steps-container::before {
    display: none;
  }
  
  .step-card {
    max-width: 400px;
    margin: 0 auto;
  }
  
  .step-number {
    font-size: 2.5rem;
  }
  
  .step-icon {
    font-size: 3rem;
  }
  
  .demo-card {
    padding: 2rem;
  }
  
  .demo-title {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .step-card {
    padding: 1.5rem;
  }
  
  .demo-card {
    padding: 1.5rem;
  }
  
  .demo-btn {
    width: 100%;
    justify-content: center;
  }
}
