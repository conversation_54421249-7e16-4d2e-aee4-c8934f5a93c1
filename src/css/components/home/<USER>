.use-cases-section {
  padding: 120px 0;
  background: var(--bg-primary);
  position: relative;
  overflow: hidden;
}

.use-cases-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 20%, rgba(78, 205, 196, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 107, 107, 0.08) 0%, transparent 50%);
  pointer-events: none;
}

.section-header {
  text-align: center;
  margin-bottom: 80px;
}

.use-cases-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-bottom: 100px;
}

.use-case-card {
  padding: 2.5rem;
  text-align: center;
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: var(--border-radius);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.use-case-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  opacity: 0;
  transition: var(--transition);
}

.use-case-card:hover::before {
  opacity: 1;
}

.use-case-card:hover {
  transform: translateY(-10px);
  background: rgba(255, 255, 255, 0.06);
  border-color: rgba(255, 255, 255, 0.15);
  box-shadow: var(--shadow-heavy);
}

.use-case-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  display: block;
  transition: var(--transition);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.use-case-card:hover .use-case-icon {
  transform: scale(1.1) rotateY(10deg);
  filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.4));
}

.use-case-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
  transition: var(--transition);
}

.use-case-card:hover .use-case-title {
  color: var(--accent-primary);
}

.use-case-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-size: 1rem;
}

.use-case-benefits {
  list-style: none;
  padding: 0;
  margin: 0;
  text-align: left;
}

.use-case-benefits li {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.use-case-benefits li::before {
  content: '✓';
  color: var(--accent-tertiary);
  font-weight: bold;
  font-size: 1rem;
}

.testimonials-section {
  text-align: center;
}

.testimonials-title {
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 3rem;
  color: var(--text-primary);
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.testimonial-card {
  padding: 2rem;
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: var(--border-radius);
  transition: var(--transition);
  text-align: left;
}

.testimonial-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.06);
  border-color: rgba(255, 255, 255, 0.15);
  box-shadow: var(--shadow-medium);
}

.testimonial-rating {
  margin-bottom: 1rem;
}

.star {
  font-size: 1.2rem;
  margin-right: 0.2rem;
}

.testimonial-quote {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  font-style: italic;
  border: none;
  padding: 0;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.author-avatar {
  font-size: 2.5rem;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  backdrop-filter: blur(10px);
}

.author-info {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.2rem;
}

.author-role {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

/* Animation delays */
.use-case-card:nth-child(1) { animation-delay: 0.1s; }
.use-case-card:nth-child(2) { animation-delay: 0.2s; }
.use-case-card:nth-child(3) { animation-delay: 0.3s; }
.use-case-card:nth-child(4) { animation-delay: 0.4s; }

.testimonial-card:nth-child(1) { animation-delay: 0.1s; }
.testimonial-card:nth-child(2) { animation-delay: 0.2s; }
.testimonial-card:nth-child(3) { animation-delay: 0.3s; }

/* Responsive design */
@media (max-width: 768px) {
  .use-cases-section {
    padding: 80px 0;
  }
  
  .use-cases-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-bottom: 80px;
  }
  
  .testimonials-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .use-case-card,
  .testimonial-card {
    padding: 2rem;
  }
  
  .use-case-icon {
    font-size: 3rem;
  }
  
  .testimonials-title {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .use-case-card,
  .testimonial-card {
    padding: 1.5rem;
  }
  
  .use-case-icon {
    font-size: 2.5rem;
  }
  
  .testimonials-title {
    font-size: 1.8rem;
  }
}
