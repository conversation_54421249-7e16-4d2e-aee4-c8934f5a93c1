.modern-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  z-index: 9999;
  overflow: hidden;
}

.loader-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.loader-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 107, 107, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(78, 205, 196, 0.1) 0%, transparent 50%);
  animation: loaderFloat 15s ease-in-out infinite;
}

@keyframes loaderFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
  }
  33% {
    transform: translateY(-20px) rotate(1deg) scale(1.02);
  }
  66% {
    transform: translateY(10px) rotate(-1deg) scale(0.98);
  }
}

.loader-content {
  position: relative;
  z-index: 1;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

.loader-animation {
  position: relative;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loader-ring {
  position: absolute;
  width: 100px;
  height: 100px;
  border: 3px solid rgba(0, 212, 255, 0.2);
  border-top: 3px solid var(--accent-primary);
  border-radius: 50%;
  animation: spin 2s linear infinite;
}

.loader-ring-inner {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 76px;
  height: 76px;
  border: 2px solid rgba(255, 107, 107, 0.2);
  border-bottom: 2px solid var(--accent-secondary);
  border-radius: 50%;
  animation: spin 1.5s linear infinite reverse;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loader-dots {
  position: absolute;
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--gradient-tertiary);
  animation: dotPulse 1.5s ease-in-out infinite;
}

.dot-1 {
  animation-delay: 0s;
}

.dot-2 {
  animation-delay: 0.2s;
}

.dot-3 {
  animation-delay: 0.4s;
}

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.loader-text {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.loading-word {
  background: var(--gradient-tertiary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: textGlow 2s ease-in-out infinite alternate;
}

@keyframes textGlow {
  0% {
    filter: brightness(1);
  }
  100% {
    filter: brightness(1.2);
  }
}

.loading-dots {
  display: flex;
  gap: 2px;
}

.dot-anim {
  color: var(--accent-primary);
  animation: dotBlink 1.5s ease-in-out infinite;
}

.dot-anim:nth-child(1) {
  animation-delay: 0s;
}

.dot-anim:nth-child(2) {
  animation-delay: 0.3s;
}

.dot-anim:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes dotBlink {
  0%, 60%, 100% {
    opacity: 0.3;
  }
  30% {
    opacity: 1;
  }
}

.loader-subtitle {
  font-size: 1rem;
  color: var(--text-secondary);
  font-weight: 400;
  opacity: 0.8;
  animation: fadeInOut 3s ease-in-out infinite;
  margin-top: -0.5rem;
}

@keyframes fadeInOut {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .loader-animation {
    width: 100px;
    height: 100px;
  }
  
  .loader-ring {
    width: 80px;
    height: 80px;
  }
  
  .loader-ring-inner {
    top: 8px;
    left: 8px;
    width: 60px;
    height: 60px;
  }
  
  .loader-text {
    font-size: 1.3rem;
  }
  
  .loader-subtitle {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .loader-animation {
    width: 80px;
    height: 80px;
  }
  
  .loader-ring {
    width: 60px;
    height: 60px;
  }
  
  .loader-ring-inner {
    top: 6px;
    left: 6px;
    width: 44px;
    height: 44px;
  }
  
  .loader-text {
    font-size: 1.2rem;
  }
  
  .loader-subtitle {
    font-size: 0.8rem;
  }
}

/* Additional cool effects */
.modern-loader::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 10% 90%, rgba(0, 212, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 90% 10%, rgba(255, 107, 107, 0.05) 0%, transparent 50%);
  animation: backgroundShift 20s ease-in-out infinite;
  z-index: 0;
}

@keyframes backgroundShift {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}
